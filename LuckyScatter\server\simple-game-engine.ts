/**
 * 🎰 SIMPLE GAME ENGINE
 * Basic, effective game logic with cascading and AI behavior prediction
 */

export interface UserBehavior {
  userId: number;
  totalSpins: number;
  totalWagered: number;
  totalWon: number;
  currentStreak: number; // positive = wins, negative = losses
  avgBetSize: number;
  sessionTime: number;
  lastActivity: Date;
  riskLevel: 'low' | 'medium' | 'high';
  playStyle: 'casual' | 'aggressive' | 'whale';
}

export interface GameDecision {
  shouldWin: boolean;
  winMultiplier: number;
  cascadeCount: number;
  reason: string;
}

export class SimpleGameEngine {
  private userBehaviors: Map<number, UserBehavior> = new Map();
  private symbols = ['9', '10', 'J', 'Q', 'K', 'A', 'WILD', 'SCATTER'];
  
  // Simple RTP control
  private targetRTP = 0.85; // 85% return to player
  private maxLossStreak = 8; // Force win after 8 losses
  private maxWinStreak = 3; // Force loss after 3 big wins

  /**
   * 🎯 Main game decision engine
   */
  public makeGameDecision(userId: number, betAmount: number): GameDecision {
    const behavior = this.getUserBehavior(userId);
    this.updateBehavior(behavior, betAmount);

    console.log(`🤖 AI analyzing user ${userId}:`, {
      streak: behavior.currentStreak,
      rtp: this.calculateUserRTP(behavior),
      playStyle: behavior.playStyle,
      riskLevel: behavior.riskLevel
    });

    // Simple RTP control logic
    const userRTP = this.calculateUserRTP(behavior);
    let shouldWin = false;
    let winMultiplier = 1;
    let cascadeCount = 0;
    let reason = '';

    // Force win conditions (simple and effective)
    if (behavior.currentStreak <= -this.maxLossStreak) {
      shouldWin = true;
      winMultiplier = 2 + Math.random() * 3; // 2x to 5x
      cascadeCount = 1 + Math.floor(Math.random() * 2); // 1-2 cascades
      reason = `Force win after ${Math.abs(behavior.currentStreak)} losses`;
    }
    // Force loss conditions
    else if (behavior.currentStreak >= this.maxWinStreak && userRTP > this.targetRTP + 0.1) {
      shouldWin = false;
      reason = `Force loss after ${behavior.currentStreak} wins, RTP too high`;
    }
    // AI behavior prediction
    else {
      const aiDecision = this.predictUserBehavior(behavior, betAmount);
      shouldWin = aiDecision.shouldWin;
      winMultiplier = aiDecision.winMultiplier;
      cascadeCount = aiDecision.cascadeCount;
      reason = aiDecision.reason;
    }

    console.log(`🎰 Game decision: ${shouldWin ? 'WIN' : 'LOSS'} - ${reason}`);

    return {
      shouldWin,
      winMultiplier,
      cascadeCount,
      reason
    };
  }

  /**
   * 🤖 AI-powered behavior prediction
   */
  private predictUserBehavior(behavior: UserBehavior, betAmount: number): GameDecision {
    let shouldWin = false;
    let winMultiplier = 1;
    let cascadeCount = 0;
    let reason = '';

    const userRTP = this.calculateUserRTP(behavior);
    const lossStreak = Math.abs(Math.min(0, behavior.currentStreak));
    const winStreak = Math.max(0, behavior.currentStreak);

    // AI decision based on user behavior patterns
    if (behavior.playStyle === 'whale') {
      // Whales need more excitement
      if (lossStreak >= 3) {
        shouldWin = true;
        winMultiplier = 3 + Math.random() * 5; // 3x to 8x
        cascadeCount = 2 + Math.floor(Math.random() * 2); // 2-3 cascades
        reason = 'AI: Whale retention - big win needed';
      } else if (winStreak >= 2) {
        shouldWin = Math.random() < 0.3; // 30% chance
        winMultiplier = 1.5 + Math.random() * 2; // 1.5x to 3.5x
        reason = 'AI: Whale cooldown period';
      } else {
        shouldWin = Math.random() < 0.4; // 40% base chance
        winMultiplier = 2 + Math.random() * 3; // 2x to 5x
        cascadeCount = Math.floor(Math.random() * 2); // 0-1 cascades
        reason = 'AI: Whale engagement';
      }
    }
    else if (behavior.playStyle === 'aggressive') {
      // Aggressive players need frequent small wins
      if (lossStreak >= 4) {
        shouldWin = true;
        winMultiplier = 1.5 + Math.random() * 2; // 1.5x to 3.5x
        cascadeCount = 1; // 1 cascade
        reason = 'AI: Aggressive player retention';
      } else {
        shouldWin = Math.random() < 0.35; // 35% chance
        winMultiplier = 1.2 + Math.random() * 1.8; // 1.2x to 3x
        reason = 'AI: Aggressive player engagement';
      }
    }
    else { // casual
      // Casual players need steady, predictable experience
      if (lossStreak >= 5) {
        shouldWin = true;
        winMultiplier = 1.5 + Math.random() * 1.5; // 1.5x to 3x
        cascadeCount = 1; // 1 cascade
        reason = 'AI: Casual player retention';
      } else if (userRTP < this.targetRTP - 0.05) {
        shouldWin = Math.random() < 0.4; // 40% chance
        winMultiplier = 1.2 + Math.random() * 1.3; // 1.2x to 2.5x
        reason = 'AI: Casual RTP adjustment';
      } else {
        shouldWin = Math.random() < 0.25; // 25% base chance
        winMultiplier = 1.1 + Math.random() * 1.4; // 1.1x to 2.5x
        reason = 'AI: Casual steady play';
      }
    }

    return { shouldWin, winMultiplier, cascadeCount, reason };
  }

  /**
   * 🎰 Generate simple grid based on decision
   */
  public generateGrid(decision: GameDecision, rng: any): string[][] {
    const grid: string[][] = [];

    if (decision.shouldWin) {
      // Create winning grid
      grid = this.createWinningGrid(decision.winMultiplier, rng);
    } else {
      // Create losing grid (completely random)
      for (let reel = 0; reel < 5; reel++) {
        grid[reel] = [];
        for (let row = 0; row < 4; row++) {
          grid[reel][row] = this.symbols[Math.floor(rng.next() * this.symbols.length)];
        }
      }
    }

    return grid;
  }

  /**
   * 🏆 Create winning grid with cascading potential
   */
  private createWinningGrid(multiplier: number, rng: any): string[][] {
    const grid: string[][] = [];
    
    // Initialize with random symbols
    for (let reel = 0; reel < 5; reel++) {
      grid[reel] = [];
      for (let row = 0; row < 4; row++) {
        grid[reel][row] = this.symbols[Math.floor(rng.next() * this.symbols.length)];
      }
    }

    // Create winning combinations based on multiplier
    const winSymbol = this.symbols[Math.floor(rng.next() * 6)]; // Exclude WILD and SCATTER for simplicity
    
    if (multiplier >= 5) {
      // Big win - create multiple lines
      this.createWinLine(grid, winSymbol, 0); // Top row
      this.createWinLine(grid, winSymbol, 1); // Second row
    } else if (multiplier >= 3) {
      // Medium win - create one strong line
      this.createWinLine(grid, winSymbol, 0);
    } else {
      // Small win - create partial line
      this.createPartialWinLine(grid, winSymbol, rng);
    }

    return grid;
  }

  /**
   * 📏 Create winning line
   */
  private createWinLine(grid: string[][], symbol: string, row: number): void {
    for (let reel = 0; reel < 3; reel++) { // First 3 reels for basic win
      grid[reel][row] = symbol;
    }
  }

  /**
   * 📏 Create partial winning line
   */
  private createPartialWinLine(grid: string[][], symbol: string, rng: any): void {
    const row = Math.floor(rng.next() * 4);
    const reelCount = 3; // Minimum for win
    
    for (let reel = 0; reel < reelCount; reel++) {
      grid[reel][row] = symbol;
    }
  }

  /**
   * 💰 Calculate user RTP
   */
  private calculateUserRTP(behavior: UserBehavior): number {
    if (behavior.totalWagered === 0) return 0;
    return behavior.totalWon / behavior.totalWagered;
  }

  /**
   * 👤 Get or create user behavior
   */
  private getUserBehavior(userId: number): UserBehavior {
    if (!this.userBehaviors.has(userId)) {
      this.userBehaviors.set(userId, {
        userId,
        totalSpins: 0,
        totalWagered: 0,
        totalWon: 0,
        currentStreak: 0,
        avgBetSize: 0,
        sessionTime: 0,
        lastActivity: new Date(),
        riskLevel: 'medium',
        playStyle: 'casual'
      });
    }
    return this.userBehaviors.get(userId)!;
  }

  /**
   * 📊 Update user behavior
   */
  private updateBehavior(behavior: UserBehavior, betAmount: number): void {
    behavior.totalSpins++;
    behavior.totalWagered += betAmount;
    behavior.avgBetSize = behavior.totalWagered / behavior.totalSpins;
    behavior.lastActivity = new Date();

    // Determine play style based on bet size
    if (behavior.avgBetSize >= 50) {
      behavior.playStyle = 'whale';
    } else if (behavior.avgBetSize >= 10) {
      behavior.playStyle = 'aggressive';
    } else {
      behavior.playStyle = 'casual';
    }

    // Determine risk level
    if (behavior.currentStreak <= -5) {
      behavior.riskLevel = 'high'; // High risk of leaving
    } else if (behavior.currentStreak >= 3) {
      behavior.riskLevel = 'low'; // Happy player
    } else {
      behavior.riskLevel = 'medium';
    }
  }

  /**
   * 🎯 Update streak after spin result
   */
  public updateStreak(userId: number, won: boolean, winAmount: number): void {
    const behavior = this.getUserBehavior(userId);
    
    if (won) {
      behavior.currentStreak = Math.max(0, behavior.currentStreak) + 1;
      behavior.totalWon += winAmount;
    } else {
      behavior.currentStreak = Math.min(0, behavior.currentStreak) - 1;
    }

    console.log(`📊 User ${userId} streak updated: ${behavior.currentStreak}, RTP: ${this.calculateUserRTP(behavior).toFixed(3)}`);
  }

  /**
   * 📈 Get user statistics
   */
  public getUserStats(userId: number): any {
    const behavior = this.getUserBehavior(userId);
    return {
      totalSpins: behavior.totalSpins,
      rtp: this.calculateUserRTP(behavior),
      currentStreak: behavior.currentStreak,
      playStyle: behavior.playStyle,
      riskLevel: behavior.riskLevel,
      avgBetSize: behavior.avgBetSize
    };
  }
}

export const simpleGameEngine = new SimpleGameEngine();
