/**
 * 🧠 ADVANCED AI SLOT ENGINE
 * Implements sophisticated casino psychology and adaptive RTP mechanics
 * Based on modern slot machine AI research and industry best practices
 */

export interface PlayerPsychProfile {
  userId: number;
  totalSpins: number;
  totalWagered: number;
  totalWon: number;
  sessionBalance: number;
  currentStreak: number; // positive = wins, negative = losses
  spinsSinceLastBonus: number;
  spinsSinceLastBigWin: number;
  avgBetSize: number;
  sessionTime: number;
  lastActivity: Date;
  riskTolerance: 'low' | 'medium' | 'high';
  playStyle: 'casual' | 'aggressive' | 'whale';
  retentionRisk: 'low' | 'medium' | 'high';
  nearMissCount: number;
  ldwCount: number; // Loss-Disguised-as-Win count
}

export interface AdvancedGameDecision {
  shouldWin: boolean;
  winMultiplier: number;
  outcomeType: 'big_win' | 'small_win' | 'near_miss' | 'ldw' | 'loss';
  symbolWeightAdjustments: { [symbol: string]: number };
  bonusForced: boolean;
  psychologicalEffect: 'celebration' | 'near_miss_excitement' | 'anticipation' | 'neutral';
  reason: string;
}

export class AdvancedAISlotEngine {
  private playerProfiles: Map<number, PlayerPsychProfile> = new Map();
  
  // AI Configuration
  private readonly TARGET_RTP = 0.85; // 85% return to player
  private readonly MAX_LOSS_STREAK = 12; // Force intervention after 12 losses
  private readonly MAX_SPINS_WITHOUT_BONUS = 50; // Force bonus after 50 spins
  private readonly NEAR_MISS_FREQUENCY = 0.15; // 15% of losing spins should be near-misses
  private readonly LDW_FREQUENCY = 0.25; // 25% of spins should be LDW when possible
  
  // Symbol weights (can be dynamically adjusted)
  private baseSymbolWeights = {
    '9': 20,      // 20% base chance
    '10': 18,     // 18% base chance  
    'J': 16,      // 16% base chance
    'Q': 14,      // 14% base chance
    'K': 12,      // 12% base chance
    'A': 10,      // 10% base chance
    'WILD': 6,    // 6% base chance
    'SCATTER': 4  // 4% base chance (reduced from previous)
  };

  /**
   * 🧠 Main AI decision engine with advanced psychology
   */
  public makeAdvancedGameDecision(userId: number, betAmount: number): AdvancedGameDecision {
    const profile = this.getPlayerProfile(userId);
    this.updatePlayerProfile(profile, betAmount);

    console.log(`🧠 AI analyzing player ${userId}:`, {
      streak: profile.currentStreak,
      rtp: this.calculateRTP(profile),
      retentionRisk: profile.retentionRisk,
      spinsSinceBonus: profile.spinsSinceLastBonus,
      playStyle: profile.playStyle
    });

    // Step 1: Check for forced interventions
    const forcedDecision = this.checkForcedInterventions(profile);
    if (forcedDecision) {
      console.log(`🎯 Forced intervention: ${forcedDecision.reason}`);
      return forcedDecision;
    }

    // Step 2: Apply adaptive RTP logic
    const adaptiveDecision = this.applyAdaptiveRTP(profile, betAmount);
    if (adaptiveDecision) {
      console.log(`📊 Adaptive RTP: ${adaptiveDecision.reason}`);
      return adaptiveDecision;
    }

    // Step 3: Apply psychological engagement mechanics
    const psychDecision = this.applyPsychologicalMechanics(profile, betAmount);
    console.log(`🧠 Psychological decision: ${psychDecision.reason}`);
    return psychDecision;
  }

  /**
   * 🚨 Check for forced interventions (prevent extreme streaks)
   */
  private checkForcedInterventions(profile: PlayerPsychProfile): AdvancedGameDecision | null {
    // Force win after extreme loss streak
    if (profile.currentStreak <= -this.MAX_LOSS_STREAK) {
      return {
        shouldWin: true,
        winMultiplier: 2.5 + Math.random() * 2.5, // 2.5x to 5x
        outcomeType: 'big_win',
        symbolWeightAdjustments: this.createWinningWeights(),
        bonusForced: false,
        psychologicalEffect: 'celebration',
        reason: `🚨 Forced win after ${Math.abs(profile.currentStreak)} losses (retention risk)`
      };
    }

    // Force bonus after too many spins
    if (profile.spinsSinceLastBonus >= this.MAX_SPINS_WITHOUT_BONUS) {
      return {
        shouldWin: true,
        winMultiplier: 1.5 + Math.random() * 1.5, // 1.5x to 3x
        outcomeType: 'big_win',
        symbolWeightAdjustments: this.createBonusWeights(),
        bonusForced: true,
        psychologicalEffect: 'celebration',
        reason: `🎁 Forced bonus after ${profile.spinsSinceLastBonus} spins`
      };
    }

    // Prevent back-to-back big wins (house protection)
    if (profile.spinsSinceLastBigWin <= 2 && this.calculateRTP(profile) > this.TARGET_RTP + 0.15) {
      return {
        shouldWin: false,
        winMultiplier: 1,
        outcomeType: 'near_miss',
        symbolWeightAdjustments: this.createNearMissWeights(),
        bonusForced: false,
        psychologicalEffect: 'near_miss_excitement',
        reason: `🛡️ Preventing consecutive big wins (house protection)`
      };
    }

    return null;
  }

  /**
   * 📊 Apply adaptive RTP based on player state
   */
  private applyAdaptiveRTP(profile: PlayerPsychProfile, betAmount: number): AdvancedGameDecision | null {
    const currentRTP = this.calculateRTP(profile);
    const rtpDifference = currentRTP - this.TARGET_RTP;

    // Player RTP too low - boost wins
    if (rtpDifference < -0.1) {
      return {
        shouldWin: true,
        winMultiplier: 1.2 + Math.random() * 0.8, // 1.2x to 2x
        outcomeType: 'small_win',
        symbolWeightAdjustments: this.createSmallWinWeights(),
        bonusForced: false,
        psychologicalEffect: 'celebration',
        reason: `📈 RTP boost (current: ${(currentRTP * 100).toFixed(1)}%, target: ${(this.TARGET_RTP * 100).toFixed(1)}%)`
      };
    }

    // Player RTP too high - reduce wins but use psychology
    if (rtpDifference > 0.1) {
      // Use LDW or near-miss instead of pure loss
      const useLDW = Math.random() < this.LDW_FREQUENCY;
      if (useLDW) {
        return {
          shouldWin: true,
          winMultiplier: 0.3 + Math.random() * 0.4, // 0.3x to 0.7x (net loss but feels like win)
          outcomeType: 'ldw',
          symbolWeightAdjustments: this.createLDWWeights(),
          bonusForced: false,
          psychologicalEffect: 'celebration',
          reason: `🎭 LDW (Loss-Disguised-as-Win) - RTP control with psychology`
        };
      } else {
        return {
          shouldWin: false,
          winMultiplier: 1,
          outcomeType: 'near_miss',
          symbolWeightAdjustments: this.createNearMissWeights(),
          bonusForced: false,
          psychologicalEffect: 'near_miss_excitement',
          reason: `🎯 Near-miss for engagement - RTP control`
        };
      }
    }

    return null;
  }

  /**
   * 🧠 Apply psychological engagement mechanics
   */
  private applyPsychologicalMechanics(profile: PlayerPsychProfile, betAmount: number): AdvancedGameDecision {
    // High retention risk - use engagement tactics
    if (profile.retentionRisk === 'high') {
      const useNearMiss = Math.random() < this.NEAR_MISS_FREQUENCY;
      if (useNearMiss) {
        return {
          shouldWin: false,
          winMultiplier: 1,
          outcomeType: 'near_miss',
          symbolWeightAdjustments: this.createNearMissWeights(),
          bonusForced: false,
          psychologicalEffect: 'near_miss_excitement',
          reason: `🎯 Near-miss for high-risk player engagement`
        };
      }
    }

    // Whale players - need bigger excitement
    if (profile.playStyle === 'whale') {
      const shouldWin = Math.random() < 0.4; // 40% win chance
      if (shouldWin) {
        return {
          shouldWin: true,
          winMultiplier: 2 + Math.random() * 4, // 2x to 6x
          outcomeType: 'big_win',
          symbolWeightAdjustments: this.createWinningWeights(),
          bonusForced: false,
          psychologicalEffect: 'celebration',
          reason: `🐋 Whale player - big win for excitement`
        };
      }
    }

    // Default behavior with psychological elements
    const shouldWin = Math.random() < 0.3; // 30% base win chance
    if (shouldWin) {
      const useLDW = Math.random() < this.LDW_FREQUENCY && profile.currentStreak < 0;
      if (useLDW) {
        return {
          shouldWin: true,
          winMultiplier: 0.4 + Math.random() * 0.4, // 0.4x to 0.8x
          outcomeType: 'ldw',
          symbolWeightAdjustments: this.createLDWWeights(),
          bonusForced: false,
          psychologicalEffect: 'celebration',
          reason: `🎭 LDW for psychological engagement`
        };
      } else {
        return {
          shouldWin: true,
          winMultiplier: 1.1 + Math.random() * 1.4, // 1.1x to 2.5x
          outcomeType: 'small_win',
          symbolWeightAdjustments: this.createSmallWinWeights(),
          bonusForced: false,
          psychologicalEffect: 'celebration',
          reason: `🎉 Regular small win`
        };
      }
    } else {
      const useNearMiss = Math.random() < this.NEAR_MISS_FREQUENCY;
      if (useNearMiss) {
        return {
          shouldWin: false,
          winMultiplier: 1,
          outcomeType: 'near_miss',
          symbolWeightAdjustments: this.createNearMissWeights(),
          bonusForced: false,
          psychologicalEffect: 'near_miss_excitement',
          reason: `🎯 Near-miss for engagement`
        };
      } else {
        return {
          shouldWin: false,
          winMultiplier: 1,
          outcomeType: 'loss',
          symbolWeightAdjustments: {},
          bonusForced: false,
          psychologicalEffect: 'neutral',
          reason: `❌ Regular loss`
        };
      }
    }
  }

  /**
   * 🎯 Create symbol weights for different outcome types
   */
  private createWinningWeights(): { [symbol: string]: number } {
    return {
      'A': 25,      // Boost high-value symbols
      'K': 20,
      'Q': 15,
      'WILD': 15,   // Boost wilds for better wins
      'SCATTER': 2  // Keep scatters rare
    };
  }

  private createBonusWeights(): { [symbol: string]: number } {
    return {
      'SCATTER': 20, // Dramatically boost scatter for bonus
      'WILD': 10,
      'A': 15
    };
  }

  private createNearMissWeights(): { [symbol: string]: number } {
    return {
      'A': 15,      // Show high-value symbols but not quite winning
      'K': 15,
      'Q': 15,
      'WILD': 8,    // Reduce wilds to prevent accidental wins
      'SCATTER': 8  // Show scatters but not enough for bonus
    };
  }

  private createLDWWeights(): { [symbol: string]: number } {
    return {
      '9': 25,      // Boost low-value symbols for small wins
      '10': 20,
      'J': 15,
      'WILD': 5,    // Reduce wilds to keep wins small
      'SCATTER': 1  // Minimal scatters
    };
  }

  private createSmallWinWeights(): { [symbol: string]: number } {
    return {
      'J': 20,      // Medium-value symbols
      'Q': 18,
      'K': 15,
      'WILD': 8,
      'SCATTER': 3
    };
  }

  /**
   * 👤 Player profile management
   */
  private getPlayerProfile(userId: number): PlayerPsychProfile {
    if (!this.playerProfiles.has(userId)) {
      this.playerProfiles.set(userId, {
        userId,
        totalSpins: 0,
        totalWagered: 0,
        totalWon: 0,
        sessionBalance: 0,
        currentStreak: 0,
        spinsSinceLastBonus: 0,
        spinsSinceLastBigWin: 0,
        avgBetSize: 0,
        sessionTime: 0,
        lastActivity: new Date(),
        riskTolerance: 'medium',
        playStyle: 'casual',
        retentionRisk: 'low',
        nearMissCount: 0,
        ldwCount: 0
      });
    }
    return this.playerProfiles.get(userId)!;
  }

  private updatePlayerProfile(profile: PlayerPsychProfile, betAmount: number): void {
    profile.totalSpins++;
    profile.totalWagered += betAmount;
    profile.avgBetSize = profile.totalWagered / profile.totalSpins;
    profile.lastActivity = new Date();
    profile.spinsSinceLastBonus++;
    profile.spinsSinceLastBigWin++;

    // Update play style based on betting patterns
    if (profile.avgBetSize >= 50) {
      profile.playStyle = 'whale';
    } else if (profile.avgBetSize >= 10) {
      profile.playStyle = 'aggressive';
    } else {
      profile.playStyle = 'casual';
    }

    // Update retention risk
    if (profile.currentStreak <= -8) {
      profile.retentionRisk = 'high';
    } else if (profile.currentStreak <= -4) {
      profile.retentionRisk = 'medium';
    } else {
      profile.retentionRisk = 'low';
    }
  }

  private calculateRTP(profile: PlayerPsychProfile): number {
    if (profile.totalWagered === 0) return 0;
    return profile.totalWon / profile.totalWagered;
  }

  /**
   * 📊 Update player state after spin result
   */
  public updatePlayerState(userId: number, won: boolean, winAmount: number, outcomeType: string): void {
    const profile = this.getPlayerProfile(userId);

    if (won) {
      profile.currentStreak = Math.max(0, profile.currentStreak) + 1;
      profile.totalWon += winAmount;

      // Track big wins
      if (outcomeType === 'big_win') {
        profile.spinsSinceLastBigWin = 0;
      }

      // Track LDW
      if (outcomeType === 'ldw') {
        profile.ldwCount++;
      }
    } else {
      profile.currentStreak = Math.min(0, profile.currentStreak) - 1;

      // Track near misses
      if (outcomeType === 'near_miss') {
        profile.nearMissCount++;
      }
    }

    // Track bonus triggers
    if (outcomeType === 'big_win' && winAmount > profile.avgBetSize * 3) {
      profile.spinsSinceLastBonus = 0;
    }

    console.log(`📊 Player ${userId} updated:`, {
      streak: profile.currentStreak,
      rtp: this.calculateRTP(profile).toFixed(3),
      retentionRisk: profile.retentionRisk,
      nearMisses: profile.nearMissCount,
      ldws: profile.ldwCount
    });
  }

  /**
   * 🎰 Generate grid with dynamic symbol weighting
   */
  public generateAdvancedGrid(decision: AdvancedGameDecision, rng: any): string[][] {
    const grid: string[][] = [];

    // Merge base weights with decision adjustments
    const activeWeights = { ...this.baseSymbolWeights };
    Object.entries(decision.symbolWeightAdjustments).forEach(([symbol, weight]) => {
      activeWeights[symbol] = weight;
    });

    // Create weighted symbol pool
    const symbolPool: string[] = [];
    Object.entries(activeWeights).forEach(([symbol, weight]) => {
      for (let i = 0; i < weight; i++) {
        symbolPool.push(symbol);
      }
    });

    // Generate grid with weighted selection
    for (let reel = 0; reel < 5; reel++) {
      grid[reel] = [];
      for (let row = 0; row < 4; row++) {
        const randomIndex = Math.floor(rng.next() * symbolPool.length);
        grid[reel][row] = symbolPool[randomIndex];
      }
    }

    // Apply outcome-specific modifications
    if (decision.outcomeType === 'near_miss') {
      this.createNearMissPattern(grid, rng);
    } else if (decision.shouldWin && decision.outcomeType !== 'ldw') {
      this.ensureWinningPattern(grid, decision.winMultiplier, rng);
    }

    return grid;
  }

  /**
   * 🎯 Create near-miss patterns for psychological engagement
   */
  private createNearMissPattern(grid: string[][], rng: any): void {
    // Create a pattern that looks like it almost won
    const highValueSymbols = ['A', 'K', 'Q'];
    const targetSymbol = highValueSymbols[Math.floor(rng.next() * highValueSymbols.length)];

    // Place 2 matching symbols in first two reels
    const row = Math.floor(rng.next() * 4);
    grid[0][row] = targetSymbol;
    grid[1][row] = targetSymbol;

    // Place a different but visually similar symbol in third reel
    const nearSymbols = { 'A': 'K', 'K': 'Q', 'Q': 'J' };
    grid[2][row] = nearSymbols[targetSymbol as keyof typeof nearSymbols] || 'J';
  }

  /**
   * 🏆 Ensure winning patterns exist
   */
  private ensureWinningPattern(grid: string[][], multiplier: number, rng: any): void {
    const symbols = ['A', 'K', 'Q', 'J'];
    const winSymbol = symbols[Math.floor(rng.next() * symbols.length)];
    const row = Math.floor(rng.next() * 4);

    // Create winning line based on multiplier
    const reelCount = multiplier >= 3 ? 5 : multiplier >= 2 ? 4 : 3;
    for (let reel = 0; reel < reelCount; reel++) {
      grid[reel][row] = winSymbol;
    }
  }

  /**
   * 📈 Get player statistics for monitoring
   */
  public getPlayerStats(userId: number): any {
    const profile = this.getPlayerProfile(userId);
    return {
      totalSpins: profile.totalSpins,
      rtp: this.calculateRTP(profile),
      currentStreak: profile.currentStreak,
      playStyle: profile.playStyle,
      retentionRisk: profile.retentionRisk,
      avgBetSize: profile.avgBetSize,
      nearMissCount: profile.nearMissCount,
      ldwCount: profile.ldwCount,
      spinsSinceLastBonus: profile.spinsSinceLastBonus
    };
  }
}

export const advancedAISlotEngine = new AdvancedAISlotEngine();
