import type { Express } from "express";
import { createServer, type Server } from "http";
import { setupAuth } from "./auth";
import { storage } from "./storage";
import { z } from "zod";
import { randomBytes, createHash } from "crypto";
import { insertDepositRequestSchema, insertWithdrawalRequestSchema } from "@shared/schema";

export function registerRoutes(app: Express): Server {
  // Authentication routes
  setupAuth(app);

  // Game session management
  app.post("/api/game/start-session", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const user = req.user!;
      const sessionId = randomBytes(32).toString('hex');
      const rngSeed = createHash('sha256').update(sessionId + Date.now().toString()).digest('hex');

      const session = await storage.createGameSession({
        userId: user.id,
        sessionId,
        startBalance: user.balance,
        currentBalance: user.balance,
        rngSeed,
        gameState: {
          currentBet: "2.50",
          autoPlay: false,
          soundEnabled: true,
          animations: true,
        },
      });

      res.json(session);
    } catch (error) {
      console.error("Start session error:", error);
      res.status(500).json({ message: "Failed to start game session" });
    }
  });

  // Spin endpoint
  app.post("/api/game/spin", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const spinSchema = z.object({
        sessionId: z.string(),
        betAmount: z.string(),
        isFreeSpinRound: z.boolean().default(false),
      });

      const { sessionId, betAmount, isFreeSpinRound } = spinSchema.parse(req.body);
      const user = req.user!;

      // Get current session
      const session = await storage.getGameSession(sessionId);
      if (!session || session.userId !== user.id) {
        return res.status(404).json({ message: "Session not found" });
      }

      const bet = parseFloat(betAmount);

      // Check if user has sufficient balance (unless free spin)
      if (!isFreeSpinRound && session.currentBalance < bet) {
        return res.status(400).json({ message: "Insufficient balance" });
      }

      // Generate spin result using crypto-secure RNG
      const spinResult = await storage.processSpin({
        sessionId,
        userId: user.id,
        betAmount,
        isFreeSpinRound,
        session,
      });

      res.json(spinResult);
    } catch (error) {
      console.error("Spin error:", error);
      res.status(500).json({ message: "Spin failed" });
    }
  });

  // Get session stats
  app.get("/api/game/session/:sessionId", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const session = await storage.getGameSession(req.params.sessionId);
      if (!session || session.userId !== req.user!.id) {
        return res.status(404).json({ message: "Session not found" });
      }

      const stats = await storage.getSessionStats(req.params.sessionId);
      res.json({ session, stats });
    } catch (error) {
      console.error("Get session error:", error);
      res.status(500).json({ message: "Failed to get session" });
    }
  });

  // Update balance
  app.patch("/api/user/balance", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const updateSchema = z.object({
        newBalance: z.string(),
        sessionId: z.string(),
      });

      const { newBalance, sessionId } = updateSchema.parse(req.body);
      const user = req.user!;

      await storage.updateUserBalance(user.id, newBalance);
      await storage.updateSessionBalance(sessionId, newBalance);

      const updatedUser = await storage.getUser(user.id);
      res.json(updatedUser);
    } catch (error) {
      console.error("Update balance error:", error);
      res.status(500).json({ message: "Failed to update balance" });
    }
  });

  // Get user statistics
  app.get("/api/user/stats", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const stats = await storage.getUserStats(req.user!.id);
      res.json(stats);
    } catch (error) {
      console.error("Get stats error:", error);
      res.status(500).json({ message: "Failed to get statistics" });
    }
  });

  // 🎁 Daily Login Bonus Routes
  app.get("/api/user/daily-bonus", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const bonusCheck = await storage.checkDailyLogin(req.user!.id);
      res.json(bonusCheck);
    } catch (error) {
      console.error("Check daily bonus error:", error);
      res.status(500).json({ message: "Failed to check daily bonus" });
    }
  });

  app.post("/api/user/claim-daily-bonus", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const result = await storage.claimDailyBonus(req.user!.id);
      res.json(result);
    } catch (error) {
      console.error("Claim daily bonus error:", error);
      res.status(400).json({ message: error instanceof Error ? error.message : "Failed to claim daily bonus" });
    }
  });

  // 🎰 Free Spins Routes
  app.get("/api/user/free-spins", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const freeSpinCheck = await storage.checkFreeSpins(req.user!.id);
      res.json(freeSpinCheck);
    } catch (error) {
      console.error("Check free spins error:", error);
      res.status(500).json({ message: "Failed to check free spins" });
    }
  });

  app.post("/api/user/claim-free-spin", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const result = await storage.claimFreeSpin(req.user!.id);
      res.json(result);
    } catch (error) {
      console.error("Claim free spin error:", error);
      res.status(400).json({ message: error instanceof Error ? error.message : "Failed to claim free spin" });
    }
  });

  // Middleware to check admin role
  const requireAdmin = (req: any, res: any, next: any) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Authentication required" });
    }
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: "Admin access required" });
    }
    next();
  };

  // ADMIN ROUTES

  // Get admin dashboard stats
  app.get("/api/admin/stats", requireAdmin, async (req, res) => {
    try {
      const stats = await storage.getAdminStats();
      res.json(stats);
    } catch (error) {
      console.error("Admin stats error:", error);
      res.status(500).json({ message: "Failed to get admin statistics" });
    }
  });

  // Get all users
  app.get("/api/admin/users", requireAdmin, async (req, res) => {
    try {
      const users = await storage.getAllUsers();
      res.json(users);
    } catch (error) {
      console.error("Get users error:", error);
      res.status(500).json({ message: "Failed to get users" });
    }
  });

  // Toggle user status
  app.patch("/api/admin/users/:userId/status", requireAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const { isActive } = z.object({ isActive: z.boolean() }).parse(req.body);

      await storage.toggleUserStatus(userId, isActive);
      res.json({ success: true });
    } catch (error) {
      console.error("Toggle user status error:", error);
      res.status(500).json({ message: "Failed to update user status" });
    }
  });

  // DEPOSIT ROUTES

  // Create deposit request (user)
  app.post("/api/deposits", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const depositData = insertDepositRequestSchema.parse({
        ...req.body,
        userId: req.user!.id
      });

      const deposit = await storage.createDepositRequest(depositData);
      res.json(deposit);
    } catch (error) {
      console.error("Create deposit error:", error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: error.errors[0].message });
      }
      res.status(500).json({ message: "Failed to create deposit request" });
    }
  });

  // Get deposit requests (admin)
  app.get("/api/admin/deposits", requireAdmin, async (req, res) => {
    try {
      const status = req.query.status as string;
      const deposits = await storage.getDepositRequests(status);
      res.json(deposits);
    } catch (error) {
      console.error("Get deposits error:", error);
      res.status(500).json({ message: "Failed to get deposit requests" });
    }
  });

  // Process deposit request (admin)
  app.patch("/api/admin/deposits/:requestId", requireAdmin, async (req, res) => {
    try {
      const requestId = parseInt(req.params.requestId);
      const { status, notes } = z.object({
        status: z.enum(['approved', 'rejected']),
        notes: z.string().optional()
      }).parse(req.body);

      await storage.processDepositRequest(requestId, status, req.user!.id, notes);
      res.json({ success: true });
    } catch (error) {
      console.error("Process deposit error:", error);
      res.status(500).json({ message: "Failed to process deposit request" });
    }
  });

  // WITHDRAWAL ROUTES

  // Create withdrawal request (user)
  app.post("/api/withdrawals", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const withdrawalData = insertWithdrawalRequestSchema.parse({
        ...req.body,
        userId: req.user!.id
      });

      const withdrawal = await storage.createWithdrawalRequest(withdrawalData);
      res.json(withdrawal);
    } catch (error) {
      console.error("Create withdrawal error:", error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: error.errors[0].message });
      }
      res.status(500).json({ message: "Failed to create withdrawal request" });
    }
  });

  // Get withdrawal requests (admin)
  app.get("/api/admin/withdrawals", requireAdmin, async (req, res) => {
    try {
      const status = req.query.status as string;
      const withdrawals = await storage.getWithdrawalRequests(status);
      res.json(withdrawals);
    } catch (error) {
      console.error("Get withdrawals error:", error);
      res.status(500).json({ message: "Failed to get withdrawal requests" });
    }
  });

  // Process withdrawal request (admin)
  app.patch("/api/admin/withdrawals/:requestId", requireAdmin, async (req, res) => {
    try {
      const requestId = parseInt(req.params.requestId);
      const { status, notes } = z.object({
        status: z.enum(['completed', 'rejected']),
        notes: z.string().optional()
      }).parse(req.body);

      await storage.processWithdrawalRequest(requestId, status, req.user!.id, notes);
      res.json({ success: true });
    } catch (error) {
      console.error("Process withdrawal error:", error);
      res.status(500).json({ message: "Failed to process withdrawal request" });
    }
  });

  // Cleanup deposit balance data endpoint
  app.post("/api/game/cleanup-balance", async (req, res) => {
    try {
      // Import the balance engines
      const { depositBalanceEngine } = await import("./deposit-balance-engine.js");
      const { dynamicReelGenerator } = await import("./dynamic-reel-generator.js");

      // Clean up old data
      depositBalanceEngine.cleanupBalances();
      dynamicReelGenerator.cleanupCooldowns();

      res.json({ success: true, message: "Deposit balance data cleaned up" });
    } catch (error) {
      console.error("Cleanup error:", error);
      res.status(500).json({ message: "Cleanup failed" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
