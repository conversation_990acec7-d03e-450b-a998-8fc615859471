import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { User } from "@shared/schema";
import { Play, Minus, Plus, Zap, RotateCcw } from "lucide-react";
import { AudioManager } from "./audio-manager";

interface SlotMachineProps {
  user: User;
}

interface GameSession {
  sessionId: string;
  currentBalance: string;
  freeSpinsRemaining: number;
  currentMultiplier: number;
}

interface SpinResult {
  grid: string[][];
  wins: any[];
  totalWin: string;
  newBalance: string;
  multiplier: number;
  scatterTriggered: boolean;
  bonusTriggered: boolean;
  freeSpinsRemaining: number;
  spinId: number;
  winAmount: number;
  winLines: any[];
  scatterCount: number;
  hasBonus: boolean;
  freeSpinsTriggered: number;
}

const betAmounts = [0.10, 0.25, 0.50, 1.00, 2.50, 5.00, 10.00, 25.00, 50.00, 100.00];

export function SlotMachine({ user }: SlotMachineProps) {
  const { toast } = useToast();
  const [gameSession, setGameSession] = useState<GameSession | null>(null);
  const [currentBet, setCurrentBet] = useState(2.50);
  const [currentBetIndex, setCurrentBetIndex] = useState(4);
  const [isSpinning, setIsSpinning] = useState(false);
  const [reelGrid, setReelGrid] = useState<string[][]>([]);
  const [lastWin, setLastWin] = useState("0.00");
  const [autoSpin, setAutoSpin] = useState(false);
  const [autoSpinCount, setAutoSpinCount] = useState(0);
  const [showWinCelebration, setShowWinCelebration] = useState(false);
  const [winAmount, setWinAmount] = useState("0.00");
  const [showBonusModal, setShowBonusModal] = useState(false);
  const [consecutiveLosses, setConsecutiveLosses] = useState(0);

  // Initialize audio manager
  const audioManager = new AudioManager();

  // Initialize game session
  useEffect(() => {
    initializeSession();
  }, []);

  // Auto-spin logic
  useEffect(() => {
    if (autoSpin && autoSpinCount > 0 && !isSpinning && gameSession) {
      const balance = parseFloat(gameSession.currentBalance);
      if (balance >= currentBet) {
        const timer = setTimeout(() => {
          performSpin();
          setAutoSpinCount(prev => prev - 1);
        }, 2000);
        return () => clearTimeout(timer);
      } else {
        setAutoSpin(false);
        setAutoSpinCount(0);
        toast({
          title: "Auto-spin stopped",
          description: "Insufficient balance to continue",
          variant: "destructive",
        });
      }
    }
  }, [autoSpin, autoSpinCount, isSpinning, gameSession, currentBet]);

  const initializeSession = async () => {
    console.log("🎮 Initializing game session...");
    try {
      console.log("🎮 Making request to /api/game/start-session");
      const response = await apiRequest("POST", "/api/game/start-session", {});
      console.log("🎮 Session response:", response);
      const session = await response.json();
      console.log("🎮 Session data:", session);
      setGameSession(session);

      // Initialize with random symbols
      const initialGrid: string[][] = [];
      for (let reel = 0; reel < 5; reel++) {
        initialGrid[reel] = [];
        for (let row = 0; row < 4; row++) {
          const symbols = ['9', '10', 'J', 'Q', 'K', 'A', 'SCATTER', 'WILD'];
          initialGrid[reel][row] = symbols[Math.floor(Math.random() * symbols.length)];
        }
      }
      setReelGrid(initialGrid);
      console.log("🎮 Game session initialized successfully!");
    } catch (error) {
      console.error("🎮 Session initialization error:", error);
      toast({
        title: "Session Error",
        description: "Failed to initialize game session",
        variant: "destructive",
      });
    }
  };

  const adjustBet = (direction: 'increase' | 'decrease') => {
    if (direction === 'increase' && currentBetIndex < betAmounts.length - 1) {
      const newIndex = currentBetIndex + 1;
      setCurrentBetIndex(newIndex);
      setCurrentBet(betAmounts[newIndex]);
    } else if (direction === 'decrease' && currentBetIndex > 0) {
      const newIndex = currentBetIndex - 1;
      setCurrentBetIndex(newIndex);
      setCurrentBet(betAmounts[newIndex]);
    }
  };

  const setMaxBet = () => {
    const maxIndex = betAmounts.length - 1;
    setCurrentBetIndex(maxIndex);
    setCurrentBet(betAmounts[maxIndex]);
  };

  const performSpin = async () => {
    console.log("🎰 Spin button clicked!");
    console.log("🎰 Game session:", gameSession);
    console.log("🎰 Is spinning:", isSpinning);

    if (!gameSession || isSpinning) {
      console.log("🎰 Spin blocked - no session or already spinning");
      return;
    }

    const balance = parseFloat(gameSession.currentBalance);
    const isFreeSpinRound = gameSession.freeSpinsRemaining > 0;

    console.log("🎰 Balance:", balance, "Current bet:", currentBet);
    console.log("🎰 Free spin round:", isFreeSpinRound);

    if (!isFreeSpinRound && balance < currentBet) {
      console.log("🎰 Insufficient balance for spin");
      toast({
        title: "Insufficient Balance",
        description: "You don't have enough credits to spin",
        variant: "destructive",
      });
      return;
    }

    console.log("🎰 Starting spin...");
    setIsSpinning(true);
    audioManager.playSpinSound();

    try {
      console.log("🎰 Making API request to /api/game/spin");
      // Call backend spin API FIRST to get final result
      const response = await apiRequest("POST", "/api/game/spin", {
        sessionId: gameSession.sessionId,
        betAmount: currentBet.toFixed(2),
        isFreeSpinRound,
      });

      console.log("🎰 API response received:", response);
      const result: SpinResult = await response.json();
      console.log("🎰 Spin result:", result);

      // Animate reels with final result (simple animation)
      await animateReelsWithResult(result.grid);

      // Update game state immediately after reels stop
      setGameSession(prev => prev ? {
        ...prev,
        currentBalance: result.newBalance,
        freeSpinsRemaining: result.freeSpinsRemaining,
        currentMultiplier: result.multiplier,
      } : null);

      setLastWin(result.totalWin);

      // Handle wins and update consecutive losses
      if (parseFloat(result.totalWin) > 0) {
        // Reset consecutive losses on win
        setConsecutiveLosses(0);

        audioManager.playWinSound(parseFloat(result.totalWin), currentBet);
        setWinAmount(result.totalWin);

        // Show celebration for big wins
        if (parseFloat(result.totalWin) >= currentBet * 10) {
          setShowWinCelebration(true);
          setTimeout(() => setShowWinCelebration(false), 3000);
        }

        // Highlight winning symbols
        highlightWinningSymbols(result.wins);
      } else {
        // Increment consecutive losses on loss
        setConsecutiveLosses(prev => prev + 1);
      }

      // Handle bonus triggers
      if (result.bonusTriggered) {
        setShowBonusModal(true);
        setTimeout(() => setShowBonusModal(false), 3000);
      }

      // Update user data in cache
      queryClient.setQueryData(["/api/user"], {
        ...user,
        balance: result.newBalance,
      });

    } catch (error) {
      console.error("🎰 Spin error:", error);

      // Check if it's an authentication error
      if (error instanceof Error && error.message.includes("401")) {
        toast({
          title: "Authentication Required",
          description: "Please log in again to continue playing",
          variant: "destructive",
        });
        // Redirect to login
        window.location.href = '/auth';
      } else {
        toast({
          title: "Spin Failed",
          description: `An error occurred while spinning: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        });
      }
    } finally {
      console.log("🎰 Spin completed, setting isSpinning to false");
      setIsSpinning(false);
    }
  };

  const animateReelsWithResult = async (finalGrid: string[][]): Promise<void> => {
    return new Promise((resolve) => {
      const reels = document.querySelectorAll('.reel-column');
      let completedReels = 0;

      reels.forEach((reel, index) => {
        reel.classList.add('reel-spinning');

        setTimeout(() => {
          reel.classList.remove('reel-spinning');
          completedReels++;

          if (completedReels === reels.length) {
            // Set final grid immediately when all reels stop
            setReelGrid(finalGrid);
            resolve();
          }
        }, 1000 + index * 200); // Stagger reel stops
      });
    });
  };


  const highlightWinningSymbols = (wins: any[]) => {
    // Clear any existing highlights
    document.querySelectorAll('.winning-symbol').forEach(el => {
      el.classList.remove('winning-symbol');
    });

    // Add highlights for winning positions
    wins.forEach(win => {
      if (win.positions && Array.isArray(win.positions)) {
        win.positions.forEach((pos: { reel: number; row: number }) => {
          const symbolElement = document.querySelector(
            `[data-reel="${pos.reel}"][data-row="${pos.row}"]`
          );
          if (symbolElement) {
            symbolElement.classList.add('winning-symbol');
            // Remove highlight after animation
            setTimeout(() => {
              symbolElement.classList.remove('winning-symbol');
            }, 3000);
          }
        });
      }
    });
  };

  const startAutoSpin = (count: number) => {
    setAutoSpinCount(count);
    setAutoSpin(true);
  };

  const stopAutoSpin = () => {
    setAutoSpin(false);
    setAutoSpinCount(0);
  };

  const getSymbolDisplay = (symbol: string) => {
    const symbolMap: { [key: string]: { emoji: string; color: string } } = {
      '9': { emoji: '9', color: 'text-gray-300' },
      '10': { emoji: '10', color: 'text-gray-300' },
      'J': { emoji: 'J', color: 'text-blue-400' },
      'Q': { emoji: 'Q', color: 'text-purple-400' },
      'K': { emoji: 'K', color: 'text-red-400' },
      'A': { emoji: 'A', color: 'text-casino-gold' },
      'SCATTER': { emoji: '⭐', color: 'text-casino-gold' },
      'WILD': { emoji: '🃏', color: 'text-casino-red' },
    };

    const symbolData = symbolMap[symbol] || { emoji: '?', color: 'text-gray-500' };
    return (
      <span className={`text-2xl font-bold ${symbolData.color}`}>
        {symbolData.emoji}
      </span>
    );
  };

  if (!gameSession) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-casino-gold border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-casino-gold font-orbitron">Initializing game...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Simple CSS animations */}
      <style>{`
        .reel-spinning {
          animation: spin 0.1s linear infinite;
        }

        .winning-symbol {
          animation: winPulse 1s ease-in-out 3;
          border-color: #ffd700 !important;
          background-color: rgba(255, 215, 0, 0.2) !important;
          box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
        }

        @keyframes spin {
          0% { transform: translateY(0); }
          100% { transform: translateY(-10px); }
        }

        @keyframes winPulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.1); }
        }
      `}</style>

      {/* Multiplier and Free Spins Displays */}
      <div className="flex justify-between items-start">
        {gameSession.currentMultiplier > 1 && (
          <Card className="glass-card border-casino-gold/50">
            <CardContent className="p-4 text-center">
              <p className="text-xs text-gray-400 uppercase">Multiplier</p>
              <p className="text-2xl font-bold text-casino-gold animate-pulse">
                x{gameSession.currentMultiplier}
              </p>
            </CardContent>
          </Card>
        )}

        {gameSession.freeSpinsRemaining > 0 && (
          <Card className="glass-card border-casino-purple/50">
            <CardContent className="p-4 text-center">
              <p className="text-xs text-gray-400 uppercase">Free Spins</p>
              <p className="text-2xl font-bold text-casino-purple animate-pulse">
                {gameSession.freeSpinsRemaining}
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Slot Machine Grid */}
      <Card className="glass-card border-casino-gold/30 p-8">
        <div className="grid grid-cols-5 gap-3 mb-6">
          {reelGrid.map((reel, reelIndex) => (
            <div key={reelIndex} className="reel-column space-y-2">
              {reel.map((symbol, rowIndex) => (
                <div
                  key={`${reelIndex}-${rowIndex}`}
                  data-reel={reelIndex}
                  data-row={rowIndex}
                  className="symbol-container glass-card border border-casino-gold/20 rounded-xl h-20 flex items-center justify-center transition-all duration-300 hover:border-casino-gold/50"
                >
                  {getSymbolDisplay(symbol)}
                </div>
              ))}
            </div>
          ))}
        </div>

        {/* Game Info */}
        <div className="text-center space-y-2">
          <p className="text-sm text-gray-400">1024 Ways to Win</p>
          <div className="h-8 flex items-center justify-center">
            {parseFloat(lastWin) > 0 ? (
              <div className="text-2xl font-bold text-casino-gold animate-pulse">
                Last Win: ${lastWin}
              </div>
            ) : (
              <div className="text-2xl font-bold text-transparent">
                Last Win: $0.00
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Controls */}
      <Card className="glass-card border-casino-gold/30 p-6">
        <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0 lg:space-x-6">

          {/* Bet Controls */}
          <div className="flex items-center space-x-4">
            <Button
              onClick={() => adjustBet('decrease')}
              disabled={currentBetIndex === 0 || isSpinning}
              size="icon"
              className="glass-card border-casino-gold/30 hover:bg-casino-gold/20"
            >
              <Minus className="w-4 h-4 text-casino-gold" />
            </Button>

            <Card className="glass-card border-casino-gold/30 min-w-[120px]">
              <CardContent className="p-3 text-center">
                <p className="text-xs text-gray-400 uppercase">Bet Amount</p>
                <p className="text-xl font-bold text-casino-gold">
                  ${currentBet.toFixed(2)}
                </p>
              </CardContent>
            </Card>

            <Button
              onClick={() => adjustBet('increase')}
              disabled={currentBetIndex === betAmounts.length - 1 || isSpinning}
              size="icon"
              className="glass-card border-casino-gold/30 hover:bg-casino-gold/20"
            >
              <Plus className="w-4 h-4 text-casino-gold" />
            </Button>

            <Button
              onClick={setMaxBet}
              disabled={isSpinning}
              className="bg-casino-red hover:bg-casino-red/80 text-white"
            >
              MAX BET
            </Button>
          </div>

          {/* Spin Button */}
          <Button
            onClick={performSpin}
            disabled={isSpinning || (!gameSession.freeSpinsRemaining && parseFloat(gameSession.currentBalance) < currentBet)}
            className="relative overflow-hidden bg-gradient-to-r from-casino-red via-red-600 to-casino-red hover:from-casino-red/80 hover:via-red-600/80 hover:to-casino-red/80 text-white font-bold px-12 py-6 text-xl"
          >
            <div className="flex items-center space-x-3">
              {isSpinning ? (
                <RotateCcw className="w-6 h-6 animate-spin" />
              ) : (
                <Play className="w-6 h-6" />
              )}
              <span>
                {isSpinning ? "SPINNING..." :
                 gameSession.freeSpinsRemaining > 0 ? "FREE SPIN" : "SPIN"}
              </span>
            </div>
          </Button>

          {/* Auto Spin Controls */}
          <div className="flex items-center space-x-2">
            {!autoSpin ? (
              <>
                <Button
                  onClick={() => startAutoSpin(10)}
                  disabled={isSpinning}
                  variant="outline"
                  size="sm"
                  className="border-casino-purple text-casino-purple hover:bg-casino-purple/20"
                >
                  AUTO 10
                </Button>
                <Button
                  onClick={() => startAutoSpin(25)}
                  disabled={isSpinning}
                  variant="outline"
                  size="sm"
                  className="border-casino-purple text-casino-purple hover:bg-casino-purple/20"
                >
                  AUTO 25
                </Button>
              </>
            ) : (
              <Button
                onClick={stopAutoSpin}
                className="bg-casino-purple hover:bg-casino-purple/80 text-white"
              >
                <Zap className="w-4 h-4 mr-2" />
                STOP AUTO ({autoSpinCount})
              </Button>
            )}
          </div>

          {/* Game Stats */}
          <Card className="glass-card border-casino-gold/30">
            <CardContent className="p-3 text-center">
              <p className="text-xs text-gray-400 uppercase">Balance</p>
              <p className="text-xl font-bold text-casino-gold">
                ${parseFloat(gameSession.currentBalance).toFixed(2)}
              </p>
            </CardContent>
          </Card>
        </div>
      </Card>

      {/* Win Celebration Modal */}
      {showWinCelebration && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 animate-in fade-in duration-300">
          <Card className="glass-card border-casino-gold/50 p-12 text-center max-w-md mx-4">
            <div className="text-6xl mb-4">🎉</div>
            <div className="text-3xl font-bold text-casino-gold mb-6 uppercase tracking-wide animate-pulse">
              BIG WIN!
            </div>
            <div className="text-5xl font-black text-transparent bg-clip-text bg-gradient-to-r from-casino-gold via-casino-red to-casino-purple mb-6">
              ${winAmount}
            </div>
            <div className="text-lg text-gray-300">Congratulations!</div>
          </Card>
        </div>
      )}

      {/* Bonus Modal */}
      {showBonusModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 animate-in fade-in duration-300">
          <Card className="glass-card border-casino-purple/50 p-12 text-center max-w-lg mx-4">
            <div className="text-6xl mb-6">⭐</div>
            <div className="text-4xl font-black text-casino-purple mb-4 uppercase tracking-wide">
              FREE SPINS ACTIVATED!
            </div>
            <div className="text-2xl font-bold text-casino-gold mb-6">
              You won {gameSession.freeSpinsRemaining} Free Spins with {gameSession.currentMultiplier}x Multiplier!
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}

